<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <title>Brandable Custom Chatbox for N8N — Dark Theme</title>
    
    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Brandable Custom Chatbox for N8N — Dark Theme" />
    <meta property="og:description" content="A lightweight, brandable chat widget that connects to n8n Webhooks. Features modern UI, dark theme, theming, and WordPress plugin support." />
    <meta property="og:url" content="https://omerfayyaz.com/n8n-brandable-chatbox/dark.html" />
    <meta property="og:image" content="https://omerfayyaz.com/n8n-brandable-chatbox/n8n-brandable-chatbox.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Brandable Custom Chatbox for N8N - Dark theme chat widget interface" />
    <meta property="og:site_name" content="Omer Fayyaz" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Brandable Custom Chatbox for N8N — Dark Theme" />
    <meta name="twitter:description" content="A lightweight, brandable chat widget that connects to n8n Webhooks. Features modern UI, dark theme, theming, and WordPress plugin support." />
    <meta name="twitter:image" content="https://omerfayyaz.com/n8n-brandable-chatbox/n8n-brandable-chatbox.jpg" />
    <meta name="twitter:image:alt" content="Brandable Custom Chatbox for N8N - Dark theme chat widget interface" />
    
    <!-- WhatsApp/Facebook specific -->
    <meta property="og:locale" content="en_US" />
    <meta property="og:image:type" content="image/jpeg" />
    
    <style>
      :root { color-scheme: dark; }
      body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; margin: 0; padding: 40px; background: #0f172a; color: #f8fafc; }
      .container { max-width: 760px; margin: 0 auto; }
      h1 { margin-bottom: 8px; color: #f8fafc; }
      h2, h3 { color: #e2e8f0; }
      p { color: #cbd5e1; }
      code { background: #1e293b; color: #e2e8f0; padding: 2px 6px; border-radius: 4px; }
      a { color: #60a5fa; }
      a:hover { color: #93c5fd; }
      pre { background: #1e293b; padding: 16px; border-radius: 8px; overflow-x: auto; }
      pre code { background: none; padding: 0; }
      ul { color: #cbd5e1; }
      hr { border-color: #334155; }
      
      /* Ensure chatbox positioning works correctly on mobile */
      @media (max-width: 768px) {
        body { padding: 20px; }
        .container { max-width: 100%; }
      }
      
      /* Prevent any CSS conflicts with chatbox positioning */
      * { box-sizing: border-box; }
      html, body { 
        position: relative; 
        overflow-x: hidden; 
        min-height: 100vh;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Brandable Custom Chatbox for N8N — Dark</h1>
      <h3>Developed by <a href="https://www.omerfayyaz.com" target="_blank">Omer Fayyaz</a></h3>
      
      <div style="margin: 20px 0; padding: 16px 20px; background: linear-gradient(135deg, #60a5fa, #22c55e); color: #0f172a; border-radius: 12px; text-align: center; box-shadow: 0 4px 12px rgba(96,165,250,0.3); border: 1px solid rgba(255,255,255,0.1);">
        <p style="margin: 0; font-size: 1.1rem; font-weight: 600;">
          📚 <strong>Open Source Project</strong> • 
          <a href="https://github.com/omerfayyaz/Brandable-Custom-Chatbox-for-N8N" target="_blank" rel="noopener noreferrer" style="color: #0f172a; text-decoration: underline; font-weight: 700;">
            View on GitHub
          </a>
        </p>
      </div>

      <img src="n8n-brandable-chatbox.jpg" alt="Brandable Custom Chatbox for N8N" style="width: 100%; height: auto; border-radius: 12px; margin: 20px 0;">
      
      <p>This page shows how to embed the chatbox and connect it to an n8n Webhook. Open the widget from the floating button at the bottom-right.</p>
      <p>
        Edit <code>webhookUrl</code> with your n8n URL. The widget persists conversation in <code>localStorage</code> under the key <code>n8n-brandable-chatbox</code>.
      </p>
      <div style="margin: 12px 0;">
        <a href="index.html" style="display:inline-block;padding:8px 12px;border-radius:8px;background:#e5e7eb;color:#111827;text-decoration:none;">Switch to light example</a>
      </div>
      
      <button onclick="window.N8NbrandableChatbox.api.open()">Open Chatbox</button>
      <button onclick="window.N8NbrandableChatbox.api.close()">Close Chatbox</button>
      <button onclick="window.N8NbrandableChatbox.api.toggle()">Toggle Chatbox</button>
      <button onclick="window.N8NbrandableChatbox.api.clear()">Clear Chatbox</button>
      <button onclick="window.N8NbrandableChatbox.api.send('Hello')">Send "Hello" Message</button>

      <hr />
      <h2>How this example works</h2>
      <p>
        The widget is loaded from <code>n8n-brandable-chatbox.js</code> and initialized with
        <code>window.N8NbrandableChatbox.init({...})</code>. After init, the same API is exposed at
        <code>window.N8NbrandableChatbox.api</code>, which is used by the buttons above.
      </p>
      <h3>Embed + init</h3>
      <pre><code>&lt;script src="/path/to/n8n-brandable-chatbox.js"&gt;&lt;/script&gt;
&lt;script&gt;
  const api = window.N8NbrandableChatbox.init({
    webhookUrl: "https://your-n8n/webhook/abc123",
    botName: "Acme Assistant",
    brandColor: "#6d28d9",
    accentColor: "#22c55e",
    position: "left",
    launcherVariant: "icon-text",
    launcherText: "Chat with us",
    darkMode: true,
    allowHTMLInResponses: true
  });
  // api.open(); api.close(); api.toggle(); api.send("Hi"); api.clear(); api.getSessionId();
&lt;/script&gt;</code></pre>

      <h3>Common options</h3>
      <ul>
        <li><code>webhookUrl</code>: your n8n Webhook URL (required)</li>
        <li><code>brandColor</code>, <code>accentColor</code>: theming</li>
        <li><code>launcherVariant</code>: <code>icon</code> | <code>text</code> | <code>icon-text</code></li>
        <li><code>darkMode</code>, <code>position</code>, <code>openByDefault</code></li>
        <li><code>allowHTMLInResponses</code>: safely render HTML replies</li>
        <li><code>extraContext</code>: appended to each request as <code>metadata</code></li>
      </ul>

      <h3>API methods</h3>
      <ul>
        <li><code>open()</code>, <code>close()</code>, <code>toggle()</code></li>
        <li><code>send(text)</code>, <code>clear()</code>, <code>getSessionId()</code></li>
      </ul>

      <h3>Webhook contract</h3>
      <p>Default request body (can be customized via <code>transformRequest</code>):</p>
      <pre><code>{
  "message": "user text",
  "sessionId": "...",
  "metadata": { }
}</code></pre>
      <p>Default parsing (can be customized via <code>transformResponse</code>): looks for <code>reply</code> → <code>message</code> → <code>text</code> → <code>output</code> or plain text.</p>

      <hr />
      
      <!-- Minimalistic Profile Section -->
      <div style="text-align: center; margin: 32px 0; padding: 24px; background: linear-gradient(135deg, #1e293b 0%, #334155 100%); border-radius: 16px; border: 1px solid #475569;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 20px; margin-bottom: 20px; flex-wrap: wrap;">
          <img src="https://avatars.githubusercontent.com/u/61316839?v=4" 
               alt="Omer Fayyaz" 
               style="width: 72px; height: 72px; border-radius: 50%; border: 3px solid #60a5fa; box-shadow: 0 8px 25px rgba(96,165,250,0.15); object-fit: cover;">
          <div style="text-align: left;">
            <h3 style="margin: 0 0 8px 0; font-size: 1.25rem; font-weight: 600; color: #f8fafc;">Omer Fayyaz</h3>
            <p style="margin: 0; color: #cbd5e1; font-size: 0.95rem; font-weight: 500;">Senior Software Engineer</p>
            <p style="margin: 4px 0 0 0; color: #94a3b8; font-size: 0.875rem;">10+ Years Experience</p>
          </div>
        </div>
        
        <!-- Social Links -->
        <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
          <a href="https://github.com/omerfayyaz" target="_blank" rel="noopener noreferrer" 
             style="display: inline-flex; align-items: center; gap: 8px; padding: 10px 18px; background: #0f172a; color: white; text-decoration: none; border-radius: 10px; font-weight: 500; font-size: 0.9rem; transition: all 0.2s ease; box-shadow: 0 2px 8px rgba(15,23,42,0.3); border: 1px solid #334155;">
            <span style="font-size: 16px;">🐙</span>
            GitHub
          </a>
          <a href="https://www.linkedin.com/in/omerfayyaz-engr/" target="_blank" rel="noopener noreferrer" 
             style="display: inline-flex; align-items: center; gap: 8px; padding: 10px 18px; background: #0ea5e9; color: white; text-decoration: none; border-radius: 10px; font-weight: 500; font-size: 0.9rem; transition: all 0.2s ease; box-shadow: 0 2px 8px rgba(14,165,233,0.2); border: 1px solid #0ea5e9;">
            <span style="font-size: 16px;">💼</span>
            LinkedIn
          </a>
        </div>
      </div>

    </div>

    <script src="n8n-brandable-chatbox.js"></script>
    <script>
      window.N8NbrandableChatbox.init({
        webhookUrl: "https://automation.vividsol.com/webhook/da571a6a-052d-46e7-b9e4-cf97f0ca1cf0",
        botName: "Brandable Custom Chatbox for N8N",
        brandColor: "#6d28d9",
        accentColor: "#22c55e",
        botAvatarUrl: "",
        userAvatarUrl: "",
        welcomeMessage: "Good evening!",
        headers: { },
        darkMode: true,
        position: "left",
        openByDefault: false,
        allowHTMLInResponses: true,
        launcherVariant: "icon-text",
        launcherText: "Chat with us",
        sessionTtlMinutes: 60,
        transformRequest: (text, ctx) => ({ message: text, sessionId: ctx.sessionId, metadata: { ...ctx.metadata, theme: "dark" } }),
        transformResponse: (data) => {
          const html = data?.html;
          if (html) return { html };
          const text = data?.reply || data?.message || data?.text || data?.output || "No response";
          return { text };
        },
      });
    </script>
  </body>
  </html>


